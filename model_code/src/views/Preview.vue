<template>
  <div class="device">
    <div class="main">
      <!-- 顶部控制区域 -->
      <div class="main-top">
        <div>
          <span>组件百分比</span>
          <i
            v-if="device === 1"
            class="el-icon-caret-bottom"
            style="font-size: 24px; position: absolute; margin-top: -3px; cursor: pointer;"
            @click="do_device(2)"
          />
          <i
            v-if="device === 2"
            class="el-icon-caret-right"
            style="font-size: 24px; position: absolute; margin-top: -3px; cursor: pointer;"
            @click="do_device(1)"
          />
        </div>

        <div>
          <span>日发电量(度)</span>
          <i
            v-if="device === 1"
            class="el-icon-caret-bottom"
            style="font-size: 24px; position: absolute; margin-top: -3px; cursor: pointer;"
            @click="do_device(2)"
          />
          <i
            v-if="device === 2"
            class="el-icon-caret-right"
            style="font-size: 24px; position: absolute; margin-top: -3px; cursor: pointer;"
            @click="do_device(1)"
          />
        </div>

        <div />
      </div>

      <!-- 设备统计标签 -->
      <div v-if="device === 2" class="main-tag">
        <div class="triangle-t">
          <div class="triangle—mian">
            <img class="triangle-icon" src="@/assets/total.png" alt="总计">
            <span>总计</span>
          </div>
          <span class="total_num">{{ device_total }}</span>
        </div>

        <div class="triangle-t">
          <div class="triangle—mian">
            <img class="triangle-icon" src="@/assets/normal.png" alt="正常">
            <span>正常</span>
          </div>
          <span class="total_num">{{ device_normal }}</span>
        </div>

        <div class="triangle-t">
          <div class="triangle—mian">
            <img class="triangle-icon" src="@/assets/abnormal.png" alt="异常">
            <span>异常</span>
          </div>
          <span class="total_num">{{ device_abnormal }}</span>
        </div>

        <div class="triangle-t">
          <div class="triangle—mian">
            <img class="triangle-icon" src="@/assets/offline.png" alt="离线">
            <span>离线</span>
          </div>
          <span class="total_num">{{ device_offline }}</span>
        </div>
      </div>
    </div>

    <!-- 画布容器 -->
    <div class="canvas-container">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <div class="loading-spinner"></div>
        <p>正在加载电站数据...</p>
      </div>

      <!-- 树形图 -->
      <vue2-org-tree
        v-else
        :data="data"
        :horizontal="true"
        name="test"
        :node-class="NodeClass"
        :label-class-name="labelClassName"
        :collapsable="true"
        :render-content="renderContent"
        :style="{ transform: `scale(${scale})` }"
        @on-expand="onExpand"
        @on-node-mouseover="onMouseover"
        @on-node-mouseout="onMouseout"
      />

      <!-- 悬浮信息框 -->
      <div v-show="BasicSwich" class="floating">
        <span class="zj_main">
          <p>组件编号:</p>
          <p class="zj_tag">{{ BasicInfo.chipId }}</p>
        </span>
        <span class="zj_main_b">
          <p>输出功率:</p>
          <p class="zj_tag">{{ BasicInfo.power }} W</p>
        </span>
        <span class="zj_main_b">
          <p>输出电压:</p>
          <p class="zj_tag">{{ BasicInfo.outputVoltage }} V</p>
        </span>
        <span class="zj_main_b">
          <p>组件温度:</p>
          <p class="zj_tag">{{ BasicInfo.componentTemperature }}℃</p>
        </span>
      </div>

      <!-- 刷新按钮 -->
      <div class="refresh-btn" @click="fetchStationData">
        <i class="el-icon-refresh" :class="{ 'rotating': loading }"></i>
      </div>
    </div>

    <!-- 底部缩略图区域 -->
    <div class="bottom1">
      <p>缩略图</p>
      <i
        v-if="min_bg === 1"
        class="el-icon-caret-bottom"
        style="font-size: 28px; position: absolute; margin-top: -25px; cursor: pointer;"
        @click="do_close(2)"
      />
      <i
        v-if="min_bg === 2"
        class="el-icon-caret-right"
        style="font-size: 28px; position: absolute; margin-top: -25px; cursor: pointer;"
        @click="do_close(1)"
      />
    </div>

    <!-- 缩略图内容 -->
    <div v-if="min_bg === 1" class="bottom" :style="{ height: height + 'px' }">
      <div class="scaled-content" :style="{ transform: `scale(${scale2})` }">
        <vue2-org-tree
          class="org-tree-container2"
          :data="data"
          :horizontal="true"
          name="test"
          :node-class="NodeClass"
          :label-class-name="labelClassName"
          :collapsable="true"
          :render-content="renderContent"
        />
      </div>
    </div>

    <!-- 缩放控制按钮 -->
    <div class="bottom_right">
      <i
        class="el-icon-circle-plus-outline"
        style="font-size: 30px; z-index: 99999;"
        @click="outline('add')"
      />
      <i
        class="el-icon-remove-outline"
        style="font-size: 30px; margin-left: 10px; z-index: 99999;"
        @click="outline('up')"
      />
    </div>
  </div>
</template>


<!-- <style src="@/styles/find.scss" lang="scss"></style>
<style src="@/styles/global.scss" lang="scss"></style> -->

 <script>
import { StationList } from '@/api/getData.js'

export default {
  name: 'Preview',
  data() {
    return {
      device: 1,
      min_bg: 1,
      scale: 1,
      scale2: 0.5,
      height: 300,
      BasicSwich: false,
      device_total: 0,
      device_normal: 0,
      device_abnormal: 0,
      device_offline: 0,
      data: {},
      BasicInfo: {
        chipId: '',
        power: 0,
        outputVoltage: 0,
        componentTemperature: 0
      },
      loading: false
    }
  },
  mounted() {
    this.fetchStationData()
  },
  computed: {
    NodeClass() {
      return 'custom-node'
    },

    labelClassName() {
      return 'custom-label'
    }
  },
  methods: {
    // 获取电站数据
    async fetchStationData() {
      try {
        this.loading = true

        // 准备请求参数 - StationList接口需要URLSearchParams格式
        const params = new URLSearchParams()

        // 根据需要添加参数，比如电站ID等
        const stationId = this.$route.query.id || this.$route.query.stationId || ''
        if (stationId) {
          params.append('stationId', stationId)
        }

        // 可以添加其他参数
        params.append('pageNo', '1')
        params.append('pageSize', '100')

        console.log('调用StationList接口，参数:', params.toString())

        const response = await StationList(params)
        console.log('StationList响应数据:', response)

        if (response && response.code === 0) {
          // 处理返回的数据并转换为vue2-org-tree需要的格式
          this.processStationData(response.data || response.reModel)
          // 更新设备统计数据
          this.updateDeviceStatistics(response.data || response.reModel)
        } else {
          console.error('获取电站数据失败:', response?.message || response?.msg || '未知错误')
          // 如果接口失败，显示模拟数据用于测试
          this.loadMockData()
        }
      } catch (error) {
        console.error('调用StationList接口失败:', error)
        // 接口调用失败时，加载模拟数据用于测试
        this.loadMockData()
      } finally {
        this.loading = false
      }
    },

    // 加载模拟数据用于测试
    loadMockData() {
      console.log('加载模拟数据用于测试')
      const mockData = {
        stationName: '测试电站',
        stationId: 'station_001',
        totalPower: 1000,
        outputVoltage: 220,
        temperature: 25,
        deviceTotal: 12,
        deviceNormal: 10,
        deviceAbnormal: 1,
        deviceOffline: 1,
        collectorList: [
          {
            collectorName: '采集器1',
            collectorId: 'collector_001',
            power: 500,
            outputVoltage: 220,
            temperature: 24,
            groupList: [
              {
                groupName: '组串1',
                groupId: 'group_001',
                power: 250,
                outputVoltage: 220,
                temperature: 23,
                componentList: [
                  {
                    componentName: '组件1',
                    chipId: 'chip_001',
                    power: 125,
                    outputVoltage: 220,
                    componentTemperature: 22,
                    status: 'normal'
                  },
                  {
                    componentName: '组件2',
                    chipId: 'chip_002',
                    power: 125,
                    outputVoltage: 220,
                    componentTemperature: 23,
                    status: 'normal'
                  }
                ]
              }
            ]
          }
        ]
      }

      this.processStationData(mockData)
      this.updateDeviceStatistics(mockData)
    },

    // 处理电站数据，转换为树形结构
    processStationData(data) {
      if (!data) {
        this.data = {}
        return
      }

      // 根据实际返回的数据结构进行处理
      // 假设返回的数据包含电站、采集器、组串、组件的层级结构
      this.data = this.convertToTreeData(data)
    },

    // 转换数据为vue2-org-tree需要的格式
    convertToTreeData(data) {
      // 根据您提供的数据格式进行转换
      // 这里需要根据实际的数据结构来实现
      const treeData = {
        label: data.stationName || '电站',
        id: data.stationId || 'station_1',
        chipId: data.stationId,
        power: data.totalPower || 0,
        outputVoltage: data.outputVoltage || 0,
        componentTemperature: data.temperature || 0,
        children: []
      }

      // 处理采集器数据
      if (data.collectorList && Array.isArray(data.collectorList)) {
        data.collectorList.forEach(collector => {
          const collectorNode = {
            label: collector.collectorName || collector.cloudName || '采集器',
            id: collector.collectorId || collector.id,
            chipId: collector.collectorId,
            power: collector.power || 0,
            outputVoltage: collector.outputVoltage || 0,
            componentTemperature: collector.temperature || 0,
            children: []
          }

          // 处理组串数据
          if (collector.groupList && Array.isArray(collector.groupList)) {
            collector.groupList.forEach(group => {
              const groupNode = {
                label: group.groupName || '组串',
                id: group.groupId || group.id,
                chipId: group.groupId,
                power: group.power || 0,
                outputVoltage: group.outputVoltage || 0,
                componentTemperature: group.temperature || 0,
                children: []
              }

              // 处理组件数据
              if (group.componentList && Array.isArray(group.componentList)) {
                group.componentList.forEach(component => {
                  const componentNode = {
                    label: component.componentName || component.chipId || '组件',
                    id: component.componentId || component.chipId,
                    chipId: component.chipId,
                    power: component.power || 0,
                    outputVoltage: component.outputVoltage || 0,
                    componentTemperature: component.componentTemperature || 0
                  }
                  groupNode.children.push(componentNode)
                })
              }

              collectorNode.children.push(groupNode)
            })
          }

          treeData.children.push(collectorNode)
        })
      }

      return treeData
    },

    // 更新设备统计数据
    updateDeviceStatistics(data) {
      if (!data) return

      // 根据实际数据结构更新统计信息
      this.device_total = data.deviceTotal || 0
      this.device_normal = data.deviceNormal || 0
      this.device_abnormal = data.deviceAbnormal || 0
      this.device_offline = data.deviceOffline || 0

      // 如果数据中没有统计信息，可以通过遍历计算
      if (!data.deviceTotal && data.collectorList) {
        this.calculateDeviceStatistics(data)
      }
    },

    // 计算设备统计数据
    calculateDeviceStatistics(data) {
      let total = 0, normal = 0, abnormal = 0, offline = 0

      if (data.collectorList && Array.isArray(data.collectorList)) {
        data.collectorList.forEach(collector => {
          if (collector.groupList && Array.isArray(collector.groupList)) {
            collector.groupList.forEach(group => {
              if (group.componentList && Array.isArray(group.componentList)) {
                group.componentList.forEach(component => {
                  total++
                  const status = component.status || component.deviceStatus
                  if (status === 'normal' || status === 1) {
                    normal++
                  } else if (status === 'abnormal' || status === 2) {
                    abnormal++
                  } else if (status === 'offline' || status === 0) {
                    offline++
                  }
                })
              }
            })
          }
        })
      }

      this.device_total = total
      this.device_normal = normal
      this.device_abnormal = abnormal
      this.device_offline = offline
    },

    do_device(type) {
      this.device = type
    },

    do_close(type) {
      this.min_bg = type
    },

    outline(type) {
      if (type === 'add') {
        this.scale = Math.min(this.scale + 0.1, 3)
      } else if (type === 'up') {
        this.scale = Math.max(this.scale - 0.1, 0.1)
      }
    },

    renderContent(h, data) {
      return h('div', {
        class: 'custom-node-content'
      }, data.label)
    },

    onExpand(_, data) {
      console.log('展开节点:', data)
    },

    onMouseover(_, data) {
      this.BasicInfo = {
        chipId: data.chipId || '',
        power: data.power || 0,
        outputVoltage: data.outputVoltage || 0,
        componentTemperature: data.componentTemperature || 0
      }
      this.BasicSwich = true
    },

    onMouseout() {
      this.BasicSwich = false
    }
  }
}
</script>

<style scoped>
.device {
  width: 100%;
  height: 100vh;
  background: white;
  position: relative;
}

.main {
  padding: 20px;
}

.main-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.main-top div {
  position: relative;
  /* padding: 10px; */
  background: white;
  /* border-radius: 4px; */
  /* box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); */
}

.main-tag {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
}

.triangle-t {
  background: white;
  padding: 15px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.triangle—mian {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 10px;
}

.triangle-icon {
  width: 20px;
  height: 20px;
}

.total_num {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
}

.canvas-container {
  position: relative;
  background: white;
  /* border-radius: 4px; */
  /* box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); */
  padding: 20px;
  margin-bottom: 20px;
  min-height: 400px;
}

.floating {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 15px;
  border-radius: 4px;
  z-index: 1000;
}

.zj_main,
.zj_main_b {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.zj_tag {
  font-weight: bold;
  margin-left: 10px;
}

.bottom1 {
  display: flex;
  align-items: center;
  padding: 10px 20px;
  background: white;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 10px;
  position: relative;
}

.bottom {
  background: white;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.scaled-content {
  transform-origin: top left;
}

.bottom_right {
  position: fixed;
  bottom: 20px;
  right: 20px;
  display: flex;
  gap: 10px;
}

.bottom_right i {
  background: white;
  border-radius: 50%;
  padding: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  transition: all 0.3s;
}

.bottom_right i:hover {
  transform: scale(1.1);
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #666;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #409eff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 刷新按钮样式 */
.refresh-btn {
  position: absolute;
  top: 20px;
  left: 20px;
  width: 40px;
  height: 40px;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  transition: all 0.3s;
  z-index: 1001;
}

.refresh-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.refresh-btn i {
  font-size: 18px;
  color: #409eff;
}

.rotating {
  animation: spin 1s linear infinite;
}

/* 树形节点样式优化 */
.custom-node-content {
  padding: 8px 12px;
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  font-size: 14px;
  color: #333;
  transition: all 0.3s;
}

.custom-node-content:hover {
  background: #f5f7fa;
  border-color: #409eff;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}
</style> 