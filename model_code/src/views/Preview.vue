<template>
  <div class="device">
    <div class="main">
      <!-- 顶部控制区域 -->
      <div class="main-top">
        <div>
          <span>组件百分比</span>
          <i
            v-if="device === 1"
            class="el-icon-caret-bottom"
            style="font-size: 24px; position: absolute; margin-top: -3px; cursor: pointer;"
            @click="do_device(2)"
          />
          <i
            v-if="device === 2"
            class="el-icon-caret-right"
            style="font-size: 24px; position: absolute; margin-top: -3px; cursor: pointer;"
            @click="do_device(1)"
          />
        </div>

        <div>
          <span>日发电量(度)</span>
          <i
            v-if="device === 1"
            class="el-icon-caret-bottom"
            style="font-size: 24px; position: absolute; margin-top: -3px; cursor: pointer;"
            @click="do_device(2)"
          />
          <i
            v-if="device === 2"
            class="el-icon-caret-right"
            style="font-size: 24px; position: absolute; margin-top: -3px; cursor: pointer;"
            @click="do_device(1)"
          />
        </div>

        <div />
      </div>

      <!-- 设备统计标签 -->
      <div v-if="device === 2" class="main-tag">
        <div class="triangle-t">
          <div class="triangle—mian">
            <img class="triangle-icon" src="@/assets/total.png" alt="总计">
            <span>总计</span>
          </div>
          <span class="total_num">{{ device_total }}</span>
        </div>

        <div class="triangle-t">
          <div class="triangle—mian">
            <img class="triangle-icon" src="@/assets/normal.png" alt="正常">
            <span>正常</span>
          </div>
          <span class="total_num">{{ device_normal }}</span>
        </div>

        <div class="triangle-t">
          <div class="triangle—mian">
            <img class="triangle-icon" src="@/assets/abnormal.png" alt="异常">
            <span>异常</span>
          </div>
          <span class="total_num">{{ device_abnormal }}</span>
        </div>

        <div class="triangle-t">
          <div class="triangle—mian">
            <img class="triangle-icon" src="@/assets/offline.png" alt="离线">
            <span>离线</span>
          </div>
          <span class="total_num">{{ device_offline }}</span>
        </div>
      </div>
    </div>

    <!-- 画布容器 -->
    <div class="canvas-container">
      <vue2-org-tree
        :data="data"
        :horizontal="true"
        name="test"
        :node-class="NodeClass"
        :label-class-name="labelClassName"
        :collapsable="true"
        :render-content="renderContent"
        :style="{ transform: `scale(${scale})` }"
        @on-expand="onExpand"
        @on-node-mouseover="onMouseover"
        @on-node-mouseout="onMouseout"
      />

      <!-- 悬浮信息框 -->
      <div v-show="BasicSwich" class="floating">
        <span class="zj_main">
          <p>组件编号:</p>
          <p class="zj_tag">{{ BasicInfo.chipId }}</p>
        </span>
        <span class="zj_main_b">
          <p>输出功率:</p>
          <p class="zj_tag">{{ BasicInfo.power }} W</p>
        </span>
        <span class="zj_main_b">
          <p>输出电压:</p>
          <p class="zj_tag">{{ BasicInfo.outputVoltage }} V</p>
        </span>
        <span class="zj_main_b">
          <p>组件温度:</p>
          <p class="zj_tag">{{ BasicInfo.componentTemperature }}℃</p>
        </span>
      </div>
    </div>

    <!-- 底部缩略图区域 -->
    <div class="bottom1">
      <p>缩略图</p>
      <i
        v-if="min_bg === 1"
        class="el-icon-caret-bottom"
        style="font-size: 28px; position: absolute; margin-top: -25px; cursor: pointer;"
        @click="do_close(2)"
      />
      <i
        v-if="min_bg === 2"
        class="el-icon-caret-right"
        style="font-size: 28px; position: absolute; margin-top: -25px; cursor: pointer;"
        @click="do_close(1)"
      />
    </div>

    <!-- 缩略图内容 -->
    <div v-if="min_bg === 1" class="bottom" :style="{ height: height + 'px' }">
      <div class="scaled-content" :style="{ transform: `scale(${scale2})` }">
        <vue2-org-tree
          class="org-tree-container2"
          :data="data"
          :horizontal="true"
          name="test"
          :node-class="NodeClass"
          :label-class-name="labelClassName"
          :collapsable="true"
          :render-content="renderContent"
        />
      </div>
    </div>

    <!-- 缩放控制按钮 -->
    <div class="bottom_right">
      <i
        class="el-icon-circle-plus-outline"
        style="font-size: 30px; z-index: 99999;"
        @click="outline('add')"
      />
      <i
        class="el-icon-remove-outline"
        style="font-size: 30px; margin-left: 10px; z-index: 99999;"
        @click="outline('up')"
      />
    </div>
  </div>
</template>


<!-- <style src="@/styles/find.scss" lang="scss"></style>
<style src="@/styles/global.scss" lang="scss"></style> -->

 <script>
export default {
  name: 'Preview',
  data() {
    return {
      device: 1,
      min_bg: 1,
      scale: 1,
      scale2: 0.5,
      height: 300,
      BasicSwich: false,
      device_total: 0,
      device_normal: 0,
      device_abnormal: 0,
      device_offline: 0,
      data: {},
      BasicInfo: {
        chipId: '',
        power: 0,
        outputVoltage: 0,
        componentTemperature: 0
      }
    }
  },
  computed: {
    NodeClass() {
      return 'custom-node'
    },

    labelClassName() {
      return 'custom-label'
    }
  },
  methods: {
    do_device(type) {
      this.device = type
    },

    do_close(type) {
      this.min_bg = type
    },

    outline(type) {
      if (type === 'add') {
        this.scale = Math.min(this.scale + 0.1, 3)
      } else if (type === 'up') {
        this.scale = Math.max(this.scale - 0.1, 0.1)
      }
    },

    // judge() {
    //   return null
    // },

    renderContent(h, data) {
      return h('div', {
        class: 'custom-node-content'
      }, data.label)
    },

    onExpand(e, data) {
      console.log('展开节点:', data)
    },

    onMouseover(e, data) {
      this.BasicInfo = {
        chipId: data.chipId || '',
        power: data.power || 0,
        outputVoltage: data.outputVoltage || 0,
        componentTemperature: data.componentTemperature || 0
      }
      this.BasicSwich = true
    },

    onMouseout() {
      this.BasicSwich = false
    }
  }
}
</script>

<style scoped>
.device {
  width: 100%;
  height: 100vh;
  background: white;
  position: relative;
}

.main {
  padding: 20px;
}

.main-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.main-top div {
  position: relative;
  /* padding: 10px; */
  background: white;
  /* border-radius: 4px; */
  /* box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); */
}

.main-tag {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
}

.triangle-t {
  background: white;
  padding: 15px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.triangle—mian {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 10px;
}

.triangle-icon {
  width: 20px;
  height: 20px;
}

.total_num {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
}

.canvas-container {
  position: relative;
  background: white;
  /* border-radius: 4px; */
  /* box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); */
  padding: 20px;
  margin-bottom: 20px;
  min-height: 400px;
}

.floating {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 15px;
  border-radius: 4px;
  z-index: 1000;
}

.zj_main,
.zj_main_b {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.zj_tag {
  font-weight: bold;
  margin-left: 10px;
}

.bottom1 {
  display: flex;
  align-items: center;
  padding: 10px 20px;
  background: white;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 10px;
  position: relative;
}

.bottom {
  background: white;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.scaled-content {
  transform-origin: top left;
}

.bottom_right {
  position: fixed;
  bottom: 20px;
  right: 20px;
  display: flex;
  gap: 10px;
}

.bottom_right i {
  background: white;
  border-radius: 50%;
  padding: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  transition: all 0.3s;
}

.bottom_right i:hover {
  transform: scale(1.1);
}
</style> 