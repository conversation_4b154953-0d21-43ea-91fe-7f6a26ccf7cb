var map = {
	"./CircleShape/Component": [
		6754,
		754
	],
	"./Group/Component": [
		1076,
		76
	],
	"./LineShape/Component": [
		9268,
		268
	],
	"./Picture/Component": [
		4078,
		78
	],
	"./RectShape/Component": [
		4218,
		218
	],
	"./VButton/Component": [
		8201,
		201
	],
	"./VChart/Component": [
		7492,
		492
	],
	"./VTable/Component": [
		1160,
		160
	],
	"./VText/Component": [
		6076,
		695
	],
	"./svgs/SVGStar/Component": [
		5910,
		910
	],
	"./svgs/SVGTriangle/Component": [
		2904,
		904
	]
};
function webpackAsyncContext(req) {
	if(!__webpack_require__.o(map, req)) {
		return Promise.resolve().then(function() {
			var e = new Error("Cannot find module '" + req + "'");
			e.code = 'MODULE_NOT_FOUND';
			throw e;
		});
	}

	var ids = map[req], id = ids[0];
	return __webpack_require__.e(ids[1]).then(function() {
		return __webpack_require__(id);
	});
}
webpackAsyncContext.keys = function() { return Object.keys(map); };
webpackAsyncContext.id = 3884;
module.exports = webpackAsyncContext;