var map = {
	"./CircleShape/Attr": [
		1752,
		189,
		372,
		178
	],
	"./Group/Attr": [
		7065,
		189,
		372,
		439
	],
	"./LineShape/Attr": [
		1755,
		189,
		372,
		737
	],
	"./Picture/Attr": [
		2203,
		189,
		372,
		497
	],
	"./RectShape/Attr": [
		9820,
		189,
		372,
		654
	],
	"./VButton/Attr": [
		3277,
		189,
		372,
		347
	],
	"./VChart/Attr": [
		705,
		189,
		372,
		100,
		631
	],
	"./VTable/Attr": [
		6169,
		189,
		372,
		655
	],
	"./VText/Attr": [
		5706,
		189,
		372,
		764
	],
	"./svgs/SVGStar/Attr": [
		6917,
		189,
		372,
		35
	],
	"./svgs/SVGTriangle/Attr": [
		5981,
		189,
		372,
		627
	]
};
function webpackAsyncContext(req) {
	if(!__webpack_require__.o(map, req)) {
		return Promise.resolve().then(function() {
			var e = new Error("Cannot find module '" + req + "'");
			e.code = 'MODULE_NOT_FOUND';
			throw e;
		});
	}

	var ids = map[req], id = ids[0];
	return Promise.all(ids.slice(1).map(__webpack_require__.e)).then(function() {
		return __webpack_require__(id);
	});
}
webpackAsyncContext.keys = function() { return Object.keys(map); };
webpackAsyncContext.id = 1302;
module.exports = webpackAsyncContext;