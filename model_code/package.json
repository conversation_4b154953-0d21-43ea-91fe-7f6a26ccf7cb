{"name": "model-code-project", "version": "1.0.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"core-js": "^3.32.0", "echarts": "^5.6.0", "element-ui": "^2.15.14", "less-loader": "^12.3.0", "mathjs": "^14.5.2", "nanoid": "^5.1.5", "vue": "^2.7.16", "vue-echarts": "^7.0.3", "vue-router": "^3.6.5", "vue-tree-color": "^1.0.0", "vuex": "^3.6.2"}, "devDependencies": {"@babel/eslint-parser": "^7.22.0", "@vue/cli-plugin-babel": "~5.0.8", "@vue/cli-plugin-eslint": "~5.0.8", "@vue/cli-plugin-router": "~5.0.8", "@vue/cli-plugin-vuex": "~5.0.8", "@vue/cli-service": "~5.0.8", "eslint": "^8.45.0", "eslint-plugin-vue": "^9.15.0", "sass": "^1.64.0", "sass-loader": "^13.3.0", "vue-template-compiler": "^2.7.16"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser", "requireConfigFile": false}, "rules": {"vue/multi-word-component-names": "off", "no-prototype-builtins": "off", "no-unused-vars": "warn", "vue/no-mutating-props": "warn"}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}